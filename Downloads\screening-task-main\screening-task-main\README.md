# Bloq Quantum Full Stack Screening Task

*Are you ready to showcase your skills and help shape the future of quantum software? This is your chance to join a cutting-edge team building next-generation technology. This screening task will help us get a peek into your creativity and problem-solving abilities!*

**How to submit:**
1. Fork this repository.
2. Complete the task to the best of your ability.
3. Provide read <NAME_EMAIL> so we can review your code.
4. Deploy it on Vercel or any deployment platform and submit the link in the form (.

Follow the [steps to start a standard Next.js app](https://nextjs.org/docs/app/getting-started/installation#run-the-development-server) to get started.
The task is described on the home page. 

We look forward to seeing what you can do. Good luck!

**For questions or clarifications: <EMAIL>**
